# CLAUDE.md

此文件为 Claude Code (claude.ai/code) 在此仓库中工作时提供指导。

## 项目概述

这是一个使用 Python 和 Pygame 实现的增强版贪吃蛇游戏。项目包含主菜单系统、游戏核心逻辑、死亡记录回放功能，以及使用 BFS 寻路算法的自动游戏功能。

## 开发命令

### 安装依赖
```bash
pip install -r requirements.txt
```

### 运行应用
```bash
python main_app.py
```

### 直接运行游戏（无菜单）
```bash
python snake_game.py
```

## 代码架构

项目被组织为模块化应用程序，包含以下文件和组件：

### 主要文件

- **main_app.py**：应用程序入口点，包含主菜单系统和死亡记录回放功能
- **snake_game.py**：核心游戏逻辑，包含所有游戏类和算法
- **requirements.txt**：项目依赖列表
- **death_record_*.json**：游戏死亡记录文件（自动生成）

### 核心组件 (snake_game.py)

- **Snake 类** (`snake_game.py:50+`)：管理蛇的移动、方向变化、成长和碰撞检测
- **Food 类**：处理食物定位和随机颜色渲染
- **BFS 寻路算法**：为自动游戏模式实现广度优先搜索
- **游戏循环**：主游戏逻辑，支持手动和自动游戏模式
- **死亡记录系统**：记录游戏过程和回放功能

### 应用功能 (main_app.py)

- **主菜单系统**：开始新游戏、死亡记录回放、退出选项
- **死亡记录管理**：文件选择和回放功能
- **用户界面**：完整的中文菜单导航系统

### 主要特性

- 主菜单系统（开始游戏、回放记录、退出）
- 箭头键手动控制
- 自动游戏模式（F5 切换），使用 BFS 寻路算法
- 动态难度调节（速度随分数增加）
- 死亡记录和回放系统
- 墙壁和自身碰撞检测
- 游戏结束界面和重新开始功能
- 完整的中文用户界面支持

### 游戏控制

#### 主菜单
- 上下箭头键：选择菜单项
- Enter：确认选择
- ESC：退出应用

#### 游戏中
- 箭头键：手动控制蛇
- F5：切换自动游戏模式
- 空格键：游戏结束后重新开始
- ESC：返回主菜单

#### 回放模式
- 空格键：暂停/继续回放
- ESC：退出回放返回菜单

## 技术细节

- 窗口大小：600x600 像素，20px 单元格网格
- 游戏速度：12 FPS + 分数/5 用于动态难度
- 自动游戏使用 BFS 寻找到食物的最短路径，同时避免碰撞
- 位置循环检测：使用deque记录最近50步防止死循环
- 死亡记录格式：JSON文件包含游戏状态、分数和时间戳
- 字体支持：优先使用SimHei中文字体，回退到Arial
- 安全检查防止蛇移动到墙壁或自身位置