import pygame
import sys
import random
import json
import datetime
from collections import deque

# 游戏窗口尺寸
WINDOW_WIDTH = 600
WINDOW_HEIGHT = 600
CELL_SIZE = 20

# 颜色定义
WHITE = (255, 255, 255)
BLACK = (0, 0, 0)
GREEN = (0, 255, 127)
RED = (255, 80, 80)
DARK_GREEN = (0, 100, 0)
GOLD = (255, 215, 0)
BG_COLOR = (30, 30, 40)

# 字体
pygame.font.init()
# 优先使用支持中文的字体
try:
    FONT = pygame.font.SysFont('SimHei', 32, bold=True)
    SCORE_FONT = pygame.font.SysFont('SimHei', 24)
except:
    FONT = pygame.font.SysFont('arial', 32, bold=True)
    SCORE_FONT = pygame.font.SysFont('consolas', 24)

# 全局变量：位置历史记录（用于检测循环）
position_history = deque(maxlen=50)  # 保存最近50步的位置

# 初始化
pygame.init()
screen = pygame.display.set_mode((WINDOW_WIDTH, WINDOW_HEIGHT))
pygame.display.set_caption('精美贪吃蛇')
clock = pygame.time.Clock()

# 绘制网格
def draw_grid():
    for x in range(0, WINDOW_WIDTH, CELL_SIZE):
        pygame.draw.line(screen, (40, 40, 60), (x, 0), (x, WINDOW_HEIGHT))
    for y in range(0, WINDOW_HEIGHT, CELL_SIZE):
        pygame.draw.line(screen, (40, 40, 60), (0, y), (WINDOW_WIDTH, y))

# 贪吃蛇类
default_snake = [(5, 10), (4, 10), (3, 10)]
class Snake:
    def __init__(self):
        self.body = list(default_snake)
        self.direction = (1, 0)
        self.grow = False

    def move(self):
        head = (self.body[0][0] + self.direction[0], self.body[0][1] + self.direction[1])
        if self.grow:
            self.body = [head] + self.body
            self.grow = False
        else:
            self.body = [head] + self.body[:-1]

    def change_direction(self, dir):
        # 防止反向
        if (dir[0] * -1, dir[1] * -1) != self.direction:
            self.direction = dir

    def eat(self):
        self.grow = True

    def hit_self(self):
        return self.body[0] in self.body[1:]

    def draw(self):
        for i, (x, y) in enumerate(self.body):
            color = GOLD if i == 0 else GREEN
            pygame.draw.rect(screen, color, (x*CELL_SIZE, y*CELL_SIZE, CELL_SIZE, CELL_SIZE), border_radius=8)
            pygame.draw.rect(screen, DARK_GREEN, (x*CELL_SIZE, y*CELL_SIZE, CELL_SIZE, CELL_SIZE), 2, border_radius=8)

# 食物类
class Food:
    def __init__(self, snake):
        self.position = self.random_position(snake)
        self.color = self.random_color()

    def random_color(self):
        # 避免和背景色、蛇身色太接近
        color_choices = [
            (255, 80, 80),    # 红
            (255, 215, 0),    # 金
            (0, 191, 255),    # 深天蓝
            (255, 105, 180),  # 粉
            (0, 255, 255),    # 青
            (255, 140, 0),    # 橙
            (186, 85, 211),   # 紫
            (50, 205, 50),    # 亮绿
        ]
        return random.choice(color_choices)

    def random_position(self, snake):
        while True:
            pos = (random.randint(0, WINDOW_WIDTH//CELL_SIZE-1), random.randint(0, WINDOW_HEIGHT//CELL_SIZE-1))
            if pos not in snake.body:
                return pos

    def draw(self):
        pygame.draw.rect(screen, self.color, (self.position[0]*CELL_SIZE, self.position[1]*CELL_SIZE, CELL_SIZE, CELL_SIZE), border_radius=8)

# 显示分数
def draw_score(score):
    text = SCORE_FONT.render(f'Score: {score}', True, GOLD)
    screen.blit(text, (10, 10))

# 游戏结束界面
def game_over_screen(score):
    screen.fill(BG_COLOR)
    over_text = FONT.render('游戏结束', True, RED)
    score_text = SCORE_FONT.render(f'最终得分: {score}', True, GOLD)
    tip_text = SCORE_FONT.render('按空格重新开始，ESC退出', True, WHITE)
    screen.blit(over_text, (WINDOW_WIDTH//2 - over_text.get_width()//2, 200))
    screen.blit(score_text, (WINDOW_WIDTH//2 - score_text.get_width()//2, 270))
    screen.blit(tip_text, (WINDOW_WIDTH//2 - tip_text.get_width()//2, 340))
    pygame.display.flip()
    waiting = True
    while waiting:
        for event in pygame.event.get():
            if event.type == pygame.QUIT:
                pygame.quit()
                sys.exit()
            if event.type == pygame.KEYDOWN:
                if event.key == pygame.K_SPACE:
                    waiting = False
                elif event.key == pygame.K_ESCAPE:
                    pygame.quit()
                    sys.exit()

def is_safe_move(snake, direction):
    # 检查移动是否安全（不会立即撞墙或撞自己）
    head = snake.body[0]
    new_head = (head[0] + direction[0], head[1] + direction[1])
    
    # 检查边界
    if not (0 <= new_head[0] < WINDOW_WIDTH//CELL_SIZE and 0 <= new_head[1] < WINDOW_HEIGHT//CELL_SIZE):
        return False
    
    # 检查是否撞到身体（除了尾巴，因为尾巴会移动）
    body_without_tail = snake.body[:-1] if not snake.grow else snake.body
    if new_head in body_without_tail:
        return False
    
    return True

def is_near_boundary(snake, direction, lookahead=3):
    # 检查移动后是否接近边界（用于早期预警）
    head = snake.body[0]
    new_head = (head[0] + direction[0], head[1] + direction[1])
    
    max_x = WINDOW_WIDTH//CELL_SIZE - 1
    max_y = WINDOW_HEIGHT//CELL_SIZE - 1
    
    # 检查是否在边界附近
    return (new_head[0] < lookahead or new_head[0] > max_x - lookahead or
            new_head[1] < lookahead or new_head[1] > max_y - lookahead)

def detect_loop_pattern(snake_length=3):
    """检测是否存在循环移动模式，根据蛇长度动态调整检测条件"""
    # 根据蛇长度动态调整最小检测步数
    min_steps_for_detection = max(15, snake_length * 3)
    
    if len(position_history) < min_steps_for_detection:
        return False
    
    # 根据蛇长度调整检测窗口大小
    window_size = min(30, max(20, snake_length * 2))
    recent_positions = list(position_history)[-window_size:]
    
    # 生成方向序列
    directions = []
    for i in range(len(recent_positions)-1):
        curr = recent_positions[i]
        next_pos = recent_positions[i+1]
        direction = (next_pos[0] - curr[0], next_pos[1] - curr[1])
        directions.append(direction)
    
    # 根据蛇长度调整检测要求
    min_directions_for_analysis = max(8, snake_length)
    if len(directions) < min_directions_for_analysis:
        return False
    
    # 方法1：检测简单的往返移动（如左右左右、上下上下） - 提高阈值
    min_pattern_length_for_detection = 6 if snake_length < 10 else 4
    for i in range(len(directions) - min_pattern_length_for_detection + 1):
        # 检测ABAB模式，但要求更长的重复
        if (i + 5 < len(directions) and
            directions[i] == directions[i+2] == directions[i+4] and 
            directions[i+1] == directions[i+3] == directions[i+5] and
            directions[i] != directions[i+1]):
            # 检查是否是相反方向
            if (directions[i][0] == -directions[i+1][0] and 
                directions[i][1] == -directions[i+1][1]):
                print("检测到往返移动循环")
                return True
    
    # 方法2：检测短循环模式 - 根据蛇长度调整
    min_pattern_len = 3 if snake_length < 15 else 2
    max_pattern_len = min(8, max(6, snake_length // 2))
    
    for pattern_len in range(min_pattern_len, max_pattern_len + 1):
        required_repeats = 4 if snake_length < 20 else 3  # 短蛇要求更多重复才算循环
        
        if len(directions) >= pattern_len * required_repeats:
            # 检查最后几个模式是否相同
            patterns = []
            for i in range(required_repeats):
                start_idx = -(pattern_len * (i + 1))
                end_idx = -(pattern_len * i) if i > 0 else None
                patterns.append(directions[start_idx:end_idx])
            
            # 所有模式都相同才认为是循环
            if all(p == patterns[0] for p in patterns):
                print(f"检测到{pattern_len}步方向循环（连续{required_repeats}次）")
                return True
    
    # 方法3：检测在小范围内的徘徊 - 根据蛇长度调整
    min_steps_for_wandering = max(15, snake_length * 2)
    if len(recent_positions) >= min_steps_for_wandering:
        # 计算移动范围，短蛇要求更小的范围才算徘徊
        steps_to_check = min_steps_for_wandering
        x_positions = [pos[0] for pos in recent_positions[-steps_to_check:]]
        y_positions = [pos[1] for pos in recent_positions[-steps_to_check:]]
        x_range = max(x_positions) - min(x_positions)
        y_range = max(y_positions) - min(y_positions)
        
        # 根据蛇长度调整徘徊判定范围
        max_range = 2 if snake_length < 10 else 3
        if x_range <= max_range and y_range <= max_range:
            print(f"检测到小范围徘徊（{max_range+1}x{max_range+1}范围内移动{steps_to_check}步）")
            return True
    
    # 方法4：检测方向频繁变化但无进展 - 根据蛇长度调整
    min_steps_for_change_analysis = max(12, snake_length)
    if len(directions) >= min_steps_for_change_analysis:
        # 计算方向变化次数
        steps_to_analyze = min_steps_for_change_analysis
        direction_changes = 0
        for i in range(len(directions) - steps_to_analyze, len(directions) - 1):
            if directions[i] != directions[i+1]:
                direction_changes += 1
        
        # 根据蛇长度调整变向阈值
        max_changes = steps_to_analyze // 2 if snake_length < 15 else steps_to_analyze * 2 // 3
        
        if direction_changes >= max_changes:
            start_pos = recent_positions[-steps_to_analyze]
            end_pos = recent_positions[-1]
            total_displacement = abs(end_pos[0] - start_pos[0]) + abs(end_pos[1] - start_pos[1])
            
            # 根据蛇长度调整位移阈值
            max_displacement = 3 if snake_length < 10 else 4
            if total_displacement <= max_displacement:
                print(f"检测到频繁变向但无进展（{steps_to_analyze}步内变向{direction_changes}次，位移{total_displacement}）")
                return True
    
    return False

def is_moving_towards_food(snake, direction, food):
    """检查移动方向是否朝向食物"""
    head = snake.body[0]
    new_head = (head[0] + direction[0], head[1] + direction[1])
    
    # 计算当前头部到食物的距离
    current_distance = abs(head[0] - food.position[0]) + abs(head[1] - food.position[1])
    # 计算移动后到食物的距离
    new_distance = abs(new_head[0] - food.position[0]) + abs(new_head[1] - food.position[1])
    
    return new_distance < current_distance

def can_reach_tail(snake, direction):
    # 检查从新头部位置是否能够到达尾巴位置（避免被困死）
    head = snake.body[0]
    new_head = (head[0] + direction[0], head[1] + direction[1])
    tail = snake.body[-1]
    
    # 模拟蛇移动后的身体
    if snake.grow:
        future_body = [new_head] + snake.body
    else:
        future_body = [new_head] + snake.body[:-1]
    
    # 使用BFS检查是否能从新头部到达尾巴位置
    queue = deque([new_head])
    visited = set([new_head])
    obstacles = set(future_body[:-1])  # 不包括尾巴
    
    while queue:
        pos = queue.popleft()
        if pos == tail:
            return True
        
        for d in [(0,-1),(0,1),(-1,0),(1,0)]:
            next_pos = (pos[0]+d[0], pos[1]+d[1])
            if (0 <= next_pos[0] < WINDOW_WIDTH//CELL_SIZE and 
                0 <= next_pos[1] < WINDOW_HEIGHT//CELL_SIZE and
                next_pos not in visited and next_pos not in obstacles):
                queue.append(next_pos)
                visited.add(next_pos)
    
    return False

def find_safe_direction(snake):
    # 找到一个安全的方向，优先保持当前方向
    directions = [(0,-1), (0,1), (-1,0), (1,0)]  # 上下左右
    current_dir = snake.direction
    
    # 首先尝试当前方向（既安全又不会被困死）
    if is_safe_move(snake, current_dir) and can_reach_tail(snake, current_dir):
        return current_dir
    
    # 尝试其他方向（既安全又不会被困死）
    for direction in directions:
        if (direction != (-current_dir[0], -current_dir[1]) and 
            is_safe_move(snake, direction) and can_reach_tail(snake, direction)):
            return direction
    
    # 如果没有完全安全的方向，至少选择不会立即死亡的方向
    if is_safe_move(snake, current_dir):
        return current_dir
    
    for direction in directions:
        if direction != (-current_dir[0], -current_dir[1]) and is_safe_move(snake, direction):
            return direction
    
    # 如果没有安全方向，返回当前方向（游戏会结束）
    return current_dir

def bfs_path(snake, food):
    # BFS找最短路径，返回下一步方向
    head = snake.body[0]
    queue = deque()
    queue.append((head, []))
    
    # 创建障碍物集合（不包括尾巴，因为蛇移动时尾巴会消失）
    obstacles = set(snake.body[:-1]) if not snake.grow else set(snake.body)
    visited = set([head])
    
    # 存储所有可行路径，按长度排序
    valid_paths = []
    
    # 检测是否在循环 - 仅在蛇足够长时才进行循环检测
    is_looping = False
    if len(snake.body) > 50:  # 仅当蛇长度超过50时才检测循环
        is_looping = detect_loop_pattern(len(snake.body))
    
    # 首先尝试找到食物的路径
    while queue:
        pos, path = queue.popleft()
        if pos == food.position:
            if path:
                first_step = path[0]
                # 基本安全检查
                if is_safe_move(snake, first_step):
                    # 计算路径得分，考虑多个因素
                    score = 0
                    # 路径越短越好
                    score += (100 - len(path))
                    # 可以到达尾巴获得额外分数
                    if can_reach_tail(snake, first_step):
                        score += 50
                    # 如果朝向食物方向加分
                    if is_moving_towards_food(snake, first_step, food):
                        score += 40
                    # 如果不会太接近边界则加分（但如果在循环中则降低要求）
                    if not is_near_boundary(snake, first_step, lookahead=2):
                        score += 30
                    elif is_looping:
                        # 如果在循环中，允许接近边界但减少分数
                        score += 10
                    
                    # 如果检测到循环，激进地打破循环
                    if is_looping:
                        score += (500 - len(path) * 30)  # 循环时极大优先短路径
                        # 如果是直接朝向食物的方向，极大加分
                        if is_moving_towards_food(snake, first_step, food):
                            score += 200
                        # 如果是最短路径（1-3步），额外奖励
                        if len(path) <= 3:
                            score += 300
                    
                    valid_paths.append((score, first_step, len(path)))
            else:
                # 已经在食物位置
                break
        
        # 限制搜索深度避免过长计算
        if len(path) > 15:
            continue
            
        for d in [(0,-1),(0,1),(-1,0),(1,0)]:
            next_pos = (pos[0]+d[0], pos[1]+d[1])
            if (0 <= next_pos[0] < WINDOW_WIDTH//CELL_SIZE and 
                0 <= next_pos[1] < WINDOW_HEIGHT//CELL_SIZE and
                next_pos not in visited and next_pos not in obstacles):
                queue.append((next_pos, path+[d]))
                visited.add(next_pos)
    
    # 如果找到了通往食物的路径，选择最优的
    if valid_paths:
        # 按得分排序，优先选择得分最高的路径
        valid_paths.sort(key=lambda x: x[0], reverse=True)
        best_direction = valid_paths[0][1]
        
        # 如果检测到循环，强制选择最短且最直接的路径
        if is_looping and valid_paths:
            # 优先选择最短路径
            valid_paths.sort(key=lambda x: x[2])  # 按路径长度排序
            for score, direction, path_length in valid_paths:
                # 选择最短且能到达尾巴的路径
                if can_reach_tail(snake, direction):
                    # 进一步检查是否朝向食物
                    if is_moving_towards_food(snake, direction, food):
                        print(f"检测到循环，强制选择最短朝向食物路径（长度: {path_length}）")
                        return direction
            # 如果没有朝向食物的最短路径，选择任何最短的安全路径
            for score, direction, path_length in valid_paths:
                if can_reach_tail(snake, direction):
                    print(f"检测到循环，强制选择最短安全路径（长度: {path_length}）")
                    return direction
            # 最后的选择：最短的基本安全路径
            print(f"检测到循环，选择最短基本路径（长度: {valid_paths[0][2]}）")
            return valid_paths[0][1]
        
        return best_direction
    
    # 如果找不到安全的食物路径，使用防困死策略：优先选择能增加活动空间的方向
    return find_best_survival_direction(snake)

def calculate_reachable_area(snake, direction):
    # 计算移动后能到达的区域大小
    head = snake.body[0]
    new_head = (head[0] + direction[0], head[1] + direction[1])
    
    if not is_safe_move(snake, direction):
        return 0
    
    # 模拟蛇移动后的身体
    if snake.grow:
        future_body = [new_head] + snake.body
    else:
        future_body = [new_head] + snake.body[:-1]
    
    # 使用BFS计算可达区域
    queue = deque([new_head])
    visited = set([new_head])
    obstacles = set(future_body)
    reachable_count = 1
    
    while queue:
        pos = queue.popleft()
        for d in [(0,-1),(0,1),(-1,0),(1,0)]:
            next_pos = (pos[0]+d[0], pos[1]+d[1])
            if (0 <= next_pos[0] < WINDOW_WIDTH//CELL_SIZE and 
                0 <= next_pos[1] < WINDOW_HEIGHT//CELL_SIZE and
                next_pos not in visited and next_pos not in obstacles):
                queue.append(next_pos)
                visited.add(next_pos)
                reachable_count += 1
    
    return reachable_count

def find_best_survival_direction(snake):
    # 找到最佳生存方向：优先考虑可达区域大小，避免追尾
    directions = [(0,-1), (0,1), (-1,0), (1,0)]  # 上下左右
    current_dir = snake.direction
    tail = snake.body[-1]
    head = snake.body[0]
    
    best_direction = None
    best_score = -1
    
    for direction in directions:
        # 避免反向移动
        if direction == (-current_dir[0], -current_dir[1]):
            continue
        
        if not is_safe_move(snake, direction):
            continue
        
        new_head = (head[0] + direction[0], head[1] + direction[1])
        
        # 计算评分
        score = 0
        
        # 1. 可达区域大小权重最高
        reachable_area = calculate_reachable_area(snake, direction)
        score += reachable_area * 10
        
        # 2. 重罚接近边界的移动（防止被困在边界）
        if is_near_boundary(snake, direction, lookahead=4):
            score -= 150  # 重罚接近边界
            # 如果蛇身太长，更重罚接近边界
            if len(snake.body) > 50:
                score -= 100
        
        # 3. 避免接近尾巴（防止追尾）
        tail_distance = abs(new_head[0] - tail[0]) + abs(new_head[1] - tail[1])
        if tail_distance > 3:  # 距离尾巴足够远时给予奖励
            score += 50
        elif tail_distance <= 1:  # 太接近尾巴时重罚
            score -= 100
        
        # 4. 优先保持当前方向（减少转向），但不在边界附近
        if direction == current_dir and not is_near_boundary(snake, direction, lookahead=2):
            score += 20
        
        # 5. 检查是否能到达尾巴（基本生存条件）
        if can_reach_tail(snake, direction):
            score += 30
        else:
            score -= 200  # 重罚无法到达尾巴的方向
        
        # 6. 长蛇额外考虑：优先选择远离中心的方向（避免在中间被困）
        if len(snake.body) > 60:  # 降低阈值，更早触发
            center_x, center_y = WINDOW_WIDTH//CELL_SIZE//2, WINDOW_HEIGHT//CELL_SIZE//2
            center_distance = abs(new_head[0] - center_x) + abs(new_head[1] - center_y)
            if center_distance > 6:  # 降低距离要求
                score += 60  # 增加奖励
        
        # 7. 对于高得分长蛇，重点考虑避免被困
        if len(snake.body) > 100:
            # 检查这个方向是否会导致更容易被困
            future_head = (head[0] + direction[0], head[1] + direction[1])
            escape_routes = 0
            for test_dir in [(0,-1), (0,1), (-1,0), (1,0)]:
                test_pos = (future_head[0] + test_dir[0], future_head[1] + test_dir[1])
                if (0 <= test_pos[0] < WINDOW_WIDTH//CELL_SIZE and 
                    0 <= test_pos[1] < WINDOW_HEIGHT//CELL_SIZE and
                    test_pos not in snake.body):
                    escape_routes += 1
            
            # 如果移动后逃路路线太少，重罚
            if escape_routes <= 1:
                score -= 300
            elif escape_routes == 2:
                score -= 100
            else:
                score += 50  # 奖励有多个逃路的方向
        
        if score > best_score:
            best_score = score
            best_direction = direction
    
    # 如果没有找到好的方向，使用原来的安全方向查找
    if best_direction is None:
        return find_safe_direction(snake)
    
    return best_direction

def save_death_record(initial_state, game_events, score, death_reason, death_time, death_position, snake_length):
    """
    保存死亡记录到文件，使用新的事件驱动格式，只记录关键事件。
    """
    timestamp = datetime.datetime.now().strftime("%Y%m%d_%H%M%S")
    filename = f"death_record_{timestamp}.json"

    # 找到最后一次吃到食物后的事件
    last_eat_index = None
    for i in range(len(game_events)-1, -1, -1):
        if game_events[i]['type'] == 'food_eaten':
            last_eat_index = i
            break
    
    # 只保留从最后一次吃到食物之后的事件（如果有的话）
    if last_eat_index is not None:
        relevant_events = game_events[last_eat_index:]
    else:
        # 如果没有吃过食物，保留所有事件
        relevant_events = game_events

    record = {
        "format_version": "2.0",  # 标记新格式版本
        "timestamp": timestamp,
        "game_info": {
            "start_time": initial_state['start_time'],
            "initial_snake": initial_state['initial_snake'],
            "initial_direction": initial_state['initial_direction'],
            "initial_food": initial_state['initial_food'],
            "initial_mode": initial_state['initial_mode']
        },
        "events": relevant_events,
        "death_info": {
            "death_time": death_time,
            "death_reason": death_reason,
            "final_score": score,
            "final_snake_length": snake_length,
            "death_position": death_position
        },
        "summary": {
            "total_events": len(relevant_events),
            "direction_changes": len([e for e in relevant_events if e['type'] == 'direction_change']),
            "foods_eaten": len([e for e in relevant_events if e['type'] == 'food_eaten']),
            "mode_switches": len([e for e in relevant_events if e['type'] == 'mode_switch'])
        }
    }

    try:
        with open(filename, 'w', encoding='utf-8') as f:
            json.dump(record, f, ensure_ascii=False, indent=2)
        print(f"死亡记录已保存到: {filename}")
        return filename
    except Exception as e:
        print(f"保存死亡记录失败: {e}")
        return None

if __name__ == '__main__':
    # 入口迁移到main_app.py，便于扩展新功能
    from main_app import run_app
    run_app() 