{"format_version": "3.0", "timestamp": "20250713_144042", "metadata": {"game_duration": 208.95, "total_score": 98, "snake_max_length": 101, "death_reason": "撞到自己"}, "replay_segment": {"description": "从最后一次吃食物到死亡的完整回放段", "start_time": 208.01179695129395, "duration": 0.94241142273, "start_state": {"snake_body": [[20, 13], [19, 13], [18, 13], [17, 13], [16, 13], [15, 13], [14, 13], [13, 13], [12, 13], [11, 13]], "snake_body_compressed": {"head": [20, 13], "segments": [{"direction": [-1, 0], "length": 90}, {"direction": [0, -1], "length": 5}, {"direction": [-1, 0], "length": 5}]}, "snake_direction": [1, 0], "food_position": [25, 15], "score": 98, "mode": "auto", "snake_length": 100}, "events": [{"type": "direction_change", "time": 208.2, "direction": [0, -1], "position": [22, 13], "mode": "auto", "reason": "避障"}, {"type": "direction_change", "time": 208.5, "direction": [1, 0], "position": [22, 10], "mode": "auto", "reason": "寻找食物"}, {"type": "direction_change", "time": 208.8, "direction": [0, 1], "position": [24, 10], "mode": "auto", "reason": "避障"}], "end_state": {"death_time": 208.95420837402344, "death_position": [22, 10], "final_snake_length": 101, "collision_type": "self_collision", "collision_details": {"head_position": [22, 10], "collision_body_index": 15, "collision_body_position": [22, 10]}}}, "optimization": {"compression_used": true, "compression_ratio": 0.75, "original_size_estimate": 2400, "compressed_size": 1800}, "validation": {"checksum": "a1b2c3d4e5f6", "replay_verified": true, "verification_timestamp": "20250713_144045"}}