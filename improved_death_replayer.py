"""
改进的死亡回放系统 - 支持精确的状态恢复和事件回放
"""
import json
import pygame
import time
from typing import Dict, List, Tuple, Any
from improved_death_recorder import SnakeBodyCompressor

class GameStateRestorer:
    """游戏状态恢复器 - 从记录中恢复完整的游戏状态"""
    
    def __init__(self):
        self.compressor = SnakeBodyCompressor()
        
    def restore_snake_from_record(self, start_state: Dict[str, Any], snake_class):
        """从记录中恢复蛇的状态"""
        snake = snake_class()
        
        # 恢复蛇身
        if 'snake_body_compressed' in start_state:
            # 使用压缩数据恢复
            snake.body = self.compressor.decompress_snake_body(start_state['snake_body_compressed'])
        else:
            # 使用原始数据
            snake.body = [tuple(pos) for pos in start_state['snake_body']]
            
        # 恢复方向
        snake.direction = tuple(start_state['snake_direction'])
        
        # 恢复生长状态（假设刚吃完食物，下一步会生长）
        snake.grow = False
        
        return snake
        
    def restore_food_from_record(self, start_state: Dict[str, Any], food_class, snake):
        """从记录中恢复食物状态"""
        food = food_class(snake)
        food.position = tuple(start_state['food_position'])
        return food

class DeathReplayer:
    """死亡回放器 - 精确回放死亡过程"""
    
    def __init__(self, screen, clock, font, score_font):
        self.screen = screen
        self.clock = clock
        self.font = font
        self.score_font = score_font
        self.restorer = GameStateRestorer()
        
    def replay_death_record_v3(self, record_file: str, snake_class, food_class, 
                              draw_functions: Dict[str, callable]):
        """回放v3.0格式的死亡记录"""
        try:
            with open(record_file, 'r', encoding='utf-8') as f:
                record = json.load(f)
                
            if record.get('format_version') != '3.0':
                raise ValueError(f"不支持的格式版本: {record.get('format_version')}")
                
            return self._replay_v3_format(record, snake_class, food_class, draw_functions)
            
        except Exception as e:
            self._show_error_message(f"回放失败: {e}")
            return False
            
    def _replay_v3_format(self, record: Dict[str, Any], snake_class, food_class, 
                         draw_functions: Dict[str, callable]) -> bool:
        """执行v3.0格式的回放"""
        replay_segment = record['replay_segment']
        start_state = replay_segment['start_state']
        events = replay_segment['events']
        end_state = replay_segment['end_state']
        
        # 恢复初始状态
        snake = self.restorer.restore_snake_from_record(start_state, snake_class)
        food = self.restorer.restore_food_from_record(start_state, food_class, snake)
        
        # 回放参数
        start_time = replay_segment['start_time']
        duration = replay_segment['duration']
        current_time = 0.0
        time_speed = 0.1  # 每帧推进的时间
        paused = False
        
        # 事件索引
        event_index = 0
        current_mode = start_state['mode']
        score = start_state['score']
        
        print(f"🎬 开始回放死亡记录")
        print(f"📊 回放段: {start_time:.1f}s - {start_time + duration:.1f}s")
        print(f"🐍 初始蛇长: {start_state['snake_length']}")
        print(f"🎯 初始分数: {score}")
        print(f"📝 事件数量: {len(events)}")
        
        while current_time < duration:
            # 处理pygame事件
            for event in pygame.event.get():
                if event.type == pygame.QUIT:
                    return False
                if event.type == pygame.KEYDOWN:
                    if event.key == pygame.K_ESCAPE:
                        return False
                    elif event.key == pygame.K_SPACE:
                        paused = not paused
                    elif event.key == pygame.K_LEFT:
                        time_speed = max(0.05, time_speed - 0.05)
                    elif event.key == pygame.K_RIGHT:
                        time_speed = min(0.5, time_speed + 0.05)
                        
            if paused:
                self.clock.tick(10)
                continue
                
            # 处理事件
            absolute_time = start_time + current_time
            while (event_index < len(events) and 
                   events[event_index]['time'] <= absolute_time):
                   
                event = events[event_index]
                self._apply_event(snake, event)
                
                if event['type'] == 'direction_change':
                    current_mode = event['mode']
                    print(f"⚡ {current_time:.1f}s: 方向改变 -> {event['direction']} ({event['mode']})")
                elif event['type'] == 'mode_switch':
                    current_mode = event['new_mode']
                    print(f"🔄 {current_time:.1f}s: 模式切换 -> {current_mode}")
                    
                event_index += 1
                
            # 移动蛇
            snake.move()
            
            # 检查是否到达死亡时间
            death_reached = current_time >= duration - 0.1
            
            # 绘制游戏画面
            self._draw_replay_frame(snake, food, score, current_time, current_mode, 
                                  death_reached, end_state, draw_functions, time_speed, paused)
            
            # 如果到达死亡时间，显示死亡信息
            if death_reached:
                self._show_death_info(end_state, current_time)
                break
                
            current_time += time_speed
            self.clock.tick(15)  # 控制回放帧率
            
        # 显示回放结束信息
        self._show_replay_end_message()
        return True
        
    def _apply_event(self, snake, event: Dict[str, Any]):
        """应用事件到游戏状态"""
        if event['type'] == 'direction_change':
            new_direction = tuple(event['direction'])
            snake.direction = new_direction
            
    def _draw_replay_frame(self, snake, food, score: int, current_time: float, 
                          current_mode: str, death_reached: bool, end_state: Dict[str, Any],
                          draw_functions: Dict[str, callable], time_speed: float, paused: bool):
        """绘制回放帧"""
        # 清屏和绘制基本元素
        self.screen.fill((0, 0, 0))  # 黑色背景
        
        if 'draw_grid' in draw_functions:
            draw_functions['draw_grid']()
            
        food.draw()
        snake.draw()
        
        if 'draw_score' in draw_functions:
            draw_functions['draw_score'](score)
            
        # 绘制回放信息
        info_y = 60
        
        # 回放状态
        status = "暂停" if paused else "播放中"
        info_text = self.score_font.render(
            f'🎬 回放{status} - 时间: {current_time:.1f}s, 模式: {current_mode}', 
            True, (0, 255, 255)
        )
        self.screen.blit(info_text, (10, info_y))
        
        # 控制提示
        control_text = self.score_font.render(
            f'空格:暂停 ←→:调速({time_speed:.2f}x) ESC:退出', 
            True, (200, 200, 200)
        )
        self.screen.blit(control_text, (10, info_y + 25))
        
        # 死亡信息
        if death_reached:
            death_text = self.score_font.render(
                f'💀 死亡: {end_state.get("collision_type", "未知")} 位置: {end_state["death_position"]}', 
                True, (255, 0, 0)
            )
            self.screen.blit(death_text, (10, info_y + 50))
            
        pygame.display.flip()
        
    def _show_death_info(self, end_state: Dict[str, Any], current_time: float):
        """显示详细的死亡信息"""
        print(f"\n💀 === 死亡回放完成 ===")
        print(f"⏰ 死亡时间: {current_time:.1f}s")
        print(f"📍 死亡位置: {end_state['death_position']}")
        print(f"🐍 最终长度: {end_state['final_snake_length']}")
        print(f"💥 碰撞类型: {end_state.get('collision_type', '未知')}")
        
    def _show_replay_end_message(self):
        """显示回放结束消息"""
        message = "回放结束，按任意键返回主菜单"
        text = self.font.render(message, True, (255, 255, 255))
        text_rect = text.get_rect(center=(self.screen.get_width()//2, self.screen.get_height()//2))
        
        self.screen.fill((0, 0, 0))
        self.screen.blit(text, text_rect)
        pygame.display.flip()
        
        # 等待按键
        waiting = True
        while waiting:
            for event in pygame.event.get():
                if event.type == pygame.QUIT or event.type == pygame.KEYDOWN:
                    waiting = False
                    
    def _show_error_message(self, message: str):
        """显示错误消息"""
        print(f"❌ {message}")
        error_text = self.font.render(f"错误: {message}", True, (255, 0, 0))
        text_rect = error_text.get_rect(center=(self.screen.get_width()//2, self.screen.get_height()//2))
        
        self.screen.fill((0, 0, 0))
        self.screen.blit(error_text, text_rect)
        pygame.display.flip()
        
        time.sleep(2)  # 显示2秒
