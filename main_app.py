import pygame
import sys
import os
import json
from snake_game import (
    WINDOW_WIDTH, WINDOW_HEIGHT, BG_COLOR, FONT, SCORE_FONT, screen, clock,
    Snake, Food, draw_grid, draw_score, game_over_screen, save_death_record, position_history, bfs_path
)

def run_app():
    while True:
        choice = show_main_menu()
        if choice == 'new_game':
            run_game()
        elif choice == 'replay':
            record_file = select_death_record_file()
            if record_file:
                replay_death_record(record_file)
        elif choice == 'exit':
            pygame.quit()
            sys.exit()

def show_main_menu():
    """显示主菜单，返回用户选择"""
    menu_items = ['开始新游戏', '读取死亡记录回放', '退出']
    selected = 0
    while True:
        screen.fill(BG_COLOR)
        title = FONT.render('贪吃蛇主菜单', True, (0,255,127))
        screen.blit(title, (WINDOW_WIDTH//2 - title.get_width()//2, 120))
        for i, item in enumerate(menu_items):
            color = (255,215,0) if i == selected else (255,255,255)
            text = SCORE_FONT.render(item, True, color)
            screen.blit(text, (WINDOW_WIDTH//2 - text.get_width()//2, 220 + i*60))
        pygame.display.flip()
        for event in pygame.event.get():
            if event.type == pygame.QUIT:
                return 'exit'
            if event.type == pygame.KEYDOWN:
                if event.key == pygame.K_UP:
                    selected = (selected - 1) % len(menu_items)
                elif event.key == pygame.K_DOWN:
                    selected = (selected + 1) % len(menu_items)
                elif event.key == pygame.K_RETURN or event.key == pygame.K_SPACE:
                    if selected == 0:
                        return 'new_game'
                    elif selected == 1:
                        return 'replay'
                    elif selected == 2:
                        return 'exit'

def select_death_record_file():
    """列出当前目录下的死亡记录文件，供用户选择"""
    files = [f for f in os.listdir('.') if f.startswith('death_record_') and f.endswith('.json')]
    if not files:
        show_message('没有找到死亡记录文件，按任意键返回')
        wait_key()
        return None
    selected = 0
    while True:
        screen.fill(BG_COLOR)
        title = FONT.render('选择死亡记录文件', True, (255,215,0))
        screen.blit(title, (WINDOW_WIDTH//2 - title.get_width()//2, 80))
        for i, f in enumerate(files):
            color = (0,255,255) if i == selected else (255,255,255)
            text = SCORE_FONT.render(f, True, color)
            screen.blit(text, (WINDOW_WIDTH//2 - text.get_width()//2, 180 + i*40))
        pygame.display.flip()
        for event in pygame.event.get():
            if event.type == pygame.QUIT:
                return None
            if event.type == pygame.KEYDOWN:
                if event.key == pygame.K_UP:
                    selected = (selected - 1) % len(files)
                elif event.key == pygame.K_DOWN:
                    selected = (selected + 1) % len(files)
                elif event.key == pygame.K_RETURN or event.key == pygame.K_SPACE:
                    return files[selected]
                elif event.key == pygame.K_ESCAPE:
                    return None

def show_message(msg):
    screen.fill(BG_COLOR)
    text = SCORE_FONT.render(msg, True, (255,255,255))
    screen.blit(text, (WINDOW_WIDTH//2 - text.get_width()//2, WINDOW_HEIGHT//2 - 20))
    pygame.display.flip()

def wait_key():
    while True:
        for event in pygame.event.get():
            if event.type == pygame.KEYDOWN or event.type == pygame.QUIT:
                return

def run_game():
    # 迁移原snake_game.py主循环逻辑
    import pygame
    import sys
    WINDOW_WIDTH = 600
    WINDOW_HEIGHT = 600
    CELL_SIZE = 20
    SCORE_FONT = pygame.font.SysFont('SimHei', 24)
    while True:
        # 清除位置历史记录
        position_history.clear()
        snake = Snake()
        food = Food(snake)
        score = 0
        running = True
        auto_mode = False
        # 新的事件记录系统
        import time
        game_start_time = time.time()
        game_events = []  # 记录关键事件
        last_direction = snake.direction  # 记录上一次的方向
        last_auto_mode = auto_mode  # 记录上一次的模式
        
        # 记录游戏初始状态
        initial_state = {
            'start_time': game_start_time,
            'initial_snake': snake.body.copy(),
            'initial_direction': snake.direction,
            'initial_food': food.position,
            'initial_mode': 'manual' if not auto_mode else 'auto'
        }
        
        while running:
            clock.tick(12 + score // 5)
            for event in pygame.event.get():
                if event.type == pygame.QUIT:
                    pygame.quit()
                    sys.exit()
                if event.type == pygame.KEYDOWN:
                    current_time = time.time() - game_start_time
                    if event.key == pygame.K_UP:
                        new_direction = (0, -1)
                        if snake.change_direction(new_direction) and new_direction != last_direction:
                            game_events.append({
                                'type': 'direction_change',
                                'time': current_time,
                                'direction': new_direction,
                                'position': snake.body[0],
                                'mode': 'manual'
                            })
                            last_direction = new_direction
                    elif event.key == pygame.K_DOWN:
                        new_direction = (0, 1)
                        if snake.change_direction(new_direction) and new_direction != last_direction:
                            game_events.append({
                                'type': 'direction_change',
                                'time': current_time,
                                'direction': new_direction,
                                'position': snake.body[0],
                                'mode': 'manual'
                            })
                            last_direction = new_direction
                    elif event.key == pygame.K_LEFT:
                        new_direction = (-1, 0)
                        if snake.change_direction(new_direction) and new_direction != last_direction:
                            game_events.append({
                                'type': 'direction_change',
                                'time': current_time,
                                'direction': new_direction,
                                'position': snake.body[0],
                                'mode': 'manual'
                            })
                            last_direction = new_direction
                    elif event.key == pygame.K_RIGHT:
                        new_direction = (1, 0)
                        if snake.change_direction(new_direction) and new_direction != last_direction:
                            game_events.append({
                                'type': 'direction_change',
                                'time': current_time,
                                'direction': new_direction,
                                'position': snake.body[0],
                                'mode': 'manual'
                            })
                            last_direction = new_direction
                    elif event.key == pygame.K_F5:
                        auto_mode = not auto_mode
                        if auto_mode != last_auto_mode:
                            game_events.append({
                                'type': 'mode_switch',
                                'time': current_time,
                                'new_mode': 'auto' if auto_mode else 'manual'
                            })
                            last_auto_mode = auto_mode
            if auto_mode:
                current_time = time.time() - game_start_time
                direction = bfs_path(snake, food)
                if snake.change_direction(direction) and direction != last_direction:
                    game_events.append({
                        'type': 'direction_change',
                        'time': current_time,
                        'direction': direction,
                        'position': snake.body[0],
                        'mode': 'auto'
                    })
                    last_direction = direction
            
            snake.move()
            # 记录位置到全局历史（用于循环检测）
            position_history.append(snake.body[0])
            # 撞墙
            head_x, head_y = snake.body[0]
            if not (0 <= head_x < WINDOW_WIDTH//CELL_SIZE and 0 <= head_y < WINDOW_HEIGHT//CELL_SIZE):
                death_reason = "撞墙"
                print(f"\n=== 游戏结束：{death_reason} ===")
                print(f"最终得分: {score}")
                print(f"最近的关键事件:")
                for event in game_events[-5:]:
                    if event['type'] == 'direction_change':
                        print(f"  {event['time']:.1f}s: 方向改变到{event['direction']}, 位置{event['position']}, {event['mode']}模式")
                    elif event['type'] == 'food_eaten':
                        print(f"  {event['time']:.1f}s: 吃到食物, 分数{event['score']}, 长度{event['snake_length']}")
                    elif event['type'] == 'mode_switch':
                        print(f"  {event['time']:.1f}s: 切换到{event['new_mode']}模式")
                # 保存死亡记录到文件
                death_time = time.time() - game_start_time
                save_death_record(initial_state, game_events, score, death_reason, death_time, snake.body[0], len(snake.body))
                running = False
                break
            # 吃到自己
            if snake.hit_self():
                death_reason = "撞到自己"
                print(f"\n=== 游戏结束：{death_reason} ===")
                print(f"最终得分: {score}")
                print(f"最近的关键事件:")
                for event in game_events[-5:]:
                    if event['type'] == 'direction_change':
                        print(f"  {event['time']:.1f}s: 方向改变到{event['direction']}, 位置{event['position']}, {event['mode']}模式")
                    elif event['type'] == 'food_eaten':
                        print(f"  {event['time']:.1f}s: 吃到食物, 分数{event['score']}, 长度{event['snake_length']}")
                    elif event['type'] == 'mode_switch':
                        print(f"  {event['time']:.1f}s: 切换到{event['new_mode']}模式")
                # 保存死亡记录到文件
                death_time = time.time() - game_start_time
                save_death_record(initial_state, game_events, score, death_reason, death_time, snake.body[0], len(snake.body))
                running = False
                break
            # 吃到食物
            if snake.body[0] == food.position:
                current_time = time.time() - game_start_time
                snake.eat()
                score += 1
                # 记录吃食物事件
                game_events.append({
                    'type': 'food_eaten',
                    'time': current_time,
                    'food_pos': food.position,
                    'score': score,
                    'snake_length': len(snake.body)
                })
                food = Food(snake)
            # 绘制
            screen.fill(BG_COLOR)
            draw_grid()
            food.draw()
            snake.draw()
            draw_score(score)
            if auto_mode:
                auto_text = SCORE_FONT.render('自动托管中(F5切换)', True, (0,255,255))
                screen.blit(auto_text, (WINDOW_WIDTH//2 - auto_text.get_width()//2, 40))
            pygame.display.flip()
        game_over_screen(score)

def replay_death_record(record_file):
    """重新设计的回放函数，支持新的事件格式"""
    with open(record_file, 'r', encoding='utf-8') as f:
        record = json.load(f)
    
    # 检查格式版本
    format_version = record.get('format_version', '1.0')
    
    if format_version == '1.0':
        # 旧格式兼容处理
        moves = record.get('last_eat_to_death_moves', [])
        if not moves:
            show_message('该记录无有效回放内容，按任意键返回')
            wait_key()
            return
        replay_old_format(moves, record)
        return
    
    # 新格式处理
    game_info = record['game_info']
    events = record['events']
    death_info = record['death_info']
    
    if not events:
        show_message('该记录无有效回放内容，按任意键返回')
        wait_key()
        return
    
    # 初始化游戏状态
    snake = Snake()
    snake.body = [tuple(pos) for pos in game_info['initial_snake']]
    snake.direction = tuple(game_info['initial_direction'])
    
    food = Food(snake)
    food.position = tuple(game_info['initial_food'])
    
    score = 0
    current_mode = game_info['initial_mode']
    paused = False
    
    # 事件回放
    event_index = 0
    current_time = 0.0
    time_speed = 0.1  # 每帧推进0.1秒
    
    print(f"开始回放死亡记录: {death_info['death_reason']}, 最终得分: {death_info['final_score']}")
    
    while event_index < len(events) or current_time < death_info['death_time']:
        # 处理pygame事件
        for event in pygame.event.get():
            if event.type == pygame.QUIT:
                return
            if event.type == pygame.KEYDOWN:
                if event.key == pygame.K_ESCAPE:
                    return
                elif event.key == pygame.K_SPACE:
                    paused = not paused
        
        if paused:
            # 暂停状态下只绘制，不更新时间
            screen.fill(BG_COLOR)
            draw_grid()
            food.draw()
            snake.draw()
            draw_score(score)
            
            info = SCORE_FONT.render(f'回放暂停 - 时间: {current_time:.1f}s (空格继续, ESC退出)', True, (255,255,0))
            screen.blit(info, (WINDOW_WIDTH//2 - info.get_width()//2, 60))
            pygame.display.flip()
            clock.tick(30)
            continue
        
        # 处理当前时间点的事件
        while event_index < len(events) and events[event_index]['time'] <= current_time:
            event = events[event_index]
            
            if event['type'] == 'direction_change':
                snake.direction = tuple(event['direction'])
                print(f"时间 {event['time']:.1f}s: 方向改变到 {event['direction']} ({event['mode']}模式)")
            elif event['type'] == 'food_eaten':
                snake.eat()
                score = event['score']
                food = Food(snake)  # 生成新食物（简化处理）
                print(f"时间 {event['time']:.1f}s: 吃到食物，分数: {score}")
            elif event['type'] == 'mode_switch':
                current_mode = event['new_mode']
                print(f"时间 {event['time']:.1f}s: 切换到{current_mode}模式")
            
            event_index += 1
        
        # 移动蛇
        snake.move()
        
        # 绘制
        screen.fill(BG_COLOR)
        draw_grid()
        food.draw()
        snake.draw()
        draw_score(score)
        
        # 显示回放信息
        info = SCORE_FONT.render(f'回放中 - 时间: {current_time:.1f}s, 模式: {current_mode} (空格暂停)', True, (0,255,255))
        screen.blit(info, (WINDOW_WIDTH//2 - info.get_width()//2, 60))
        
        if current_time >= death_info['death_time']:
            death_text = SCORE_FONT.render(f'死亡: {death_info["death_reason"]}', True, (255,0,0))
            screen.blit(death_text, (WINDOW_WIDTH//2 - death_text.get_width()//2, 90))
        
        pygame.display.flip()
        clock.tick(10)  # 回放速度
        
        current_time += time_speed
    
    show_message('回放结束，按任意键返回')
    wait_key()

def replay_old_format(moves, record):
    """兼容旧格式的回放函数"""
    snake = Snake()
    snake.body = [tuple(moves[0]['snake_head'])]
    food = Food(snake)
    food.position = tuple(moves[0]['food_position'])
    
    for move in moves:
        for event in pygame.event.get():
            if event.type == pygame.QUIT:
                return
            if event.type == pygame.KEYDOWN and event.key == pygame.K_ESCAPE:
                return
        
        snake.body[0] = tuple(move['snake_head'])
        food.position = tuple(move['food_position'])
        
        screen.fill(BG_COLOR)
        draw_grid()
        food.draw()
        snake.draw()
        draw_score(move['score'])
        info = SCORE_FONT.render(f'回放(旧格式): step {move["step"]}', True, (0,255,255))
        screen.blit(info, (WINDOW_WIDTH//2 - info.get_width()//2, 60))
        pygame.display.flip()
        clock.tick(10)
    
    show_message('回放结束，按任意键返回')
    wait_key() 

if __name__ == '__main__':
    run_app() 