"""
改进的死亡记录系统 - 支持完整状态记录和智能压缩
"""
import json
import datetime
import hashlib
from typing import List, Dict, Tuple, Any
from collections import deque

class GameStateRecorder:
    """游戏状态记录器 - 负责记录和管理游戏状态快照"""
    
    def __init__(self):
        self.last_food_state = None  # 最后一次吃食物时的完整状态
        self.events_since_last_food = []  # 自上次吃食物以来的事件
        self.game_start_time = None
        
    def record_food_eaten(self, snake, food, score: int, game_time: float, mode: str):
        """记录吃食物事件，同时保存完整的游戏状态作为潜在回放起点"""
        # 保存当前完整状态作为回放起点
        self.last_food_state = {
            'time': game_time,
            'snake_body': list(snake.body),  # 完整蛇身
            'snake_direction': list(snake.direction),
            'food_position': list(food.position),
            'score': score,
            'mode': mode,
            'snake_length': len(snake.body)
        }
        
        # 记录吃食物事件
        food_event = {
            'type': 'food_eaten',
            'time': game_time,
            'food_pos': list(food.position),
            'score': score,
            'snake_length': len(snake.body)
        }
        
        # 清空之前的事件，开始新的记录段
        self.events_since_last_food = [food_event]
        
    def record_direction_change(self, direction: Tuple[int, int], position: Tuple[int, int], 
                              game_time: float, mode: str, reason: str = ""):
        """记录方向改变事件"""
        if self.last_food_state is None:
            return  # 如果还没有吃过食物，不记录
            
        direction_event = {
            'type': 'direction_change',
            'time': game_time,
            'direction': list(direction),
            'position': list(position),
            'mode': mode,
            'reason': reason
        }
        
        self.events_since_last_food.append(direction_event)
        
    def record_mode_switch(self, new_mode: str, game_time: float):
        """记录模式切换事件"""
        if self.last_food_state is None:
            return
            
        mode_event = {
            'type': 'mode_switch',
            'time': game_time,
            'new_mode': new_mode
        }
        
        self.events_since_last_food.append(mode_event)

class SnakeBodyCompressor:
    """蛇身压缩器 - 将长蛇身压缩为段式表示"""
    
    @staticmethod
    def compress_snake_body(snake_body: List[Tuple[int, int]]) -> Dict[str, Any]:
        """
        将蛇身压缩为段式表示
        例如：[(5,10), (4,10), (3,10), (2,10)] -> {head: [5,10], segments: [{direction: [-1,0], length: 3}]}
        """
        if not snake_body:
            return {"head": None, "segments": []}
            
        head = list(snake_body[0])
        segments = []
        
        if len(snake_body) == 1:
            return {"head": head, "segments": []}
            
        current_direction = None
        current_length = 0
        
        for i in range(1, len(snake_body)):
            prev_pos = snake_body[i-1]
            curr_pos = snake_body[i]
            
            # 计算当前段的方向
            direction = (curr_pos[0] - prev_pos[0], curr_pos[1] - prev_pos[1])
            
            if direction == current_direction:
                # 继续当前段
                current_length += 1
            else:
                # 开始新段
                if current_direction is not None:
                    segments.append({
                        "direction": list(current_direction),
                        "length": current_length
                    })
                current_direction = direction
                current_length = 1
                
        # 添加最后一段
        if current_direction is not None:
            segments.append({
                "direction": list(current_direction),
                "length": current_length
            })
            
        return {"head": head, "segments": segments}
    
    @staticmethod
    def decompress_snake_body(compressed_data: Dict[str, Any]) -> List[Tuple[int, int]]:
        """从压缩数据恢复完整蛇身"""
        head = tuple(compressed_data["head"])
        segments = compressed_data["segments"]
        
        body = [head]
        current_pos = head
        
        for segment in segments:
            direction = tuple(segment["direction"])
            length = segment["length"]
            
            for _ in range(length):
                current_pos = (current_pos[0] + direction[0], current_pos[1] + direction[1])
                body.append(current_pos)
                
        return body

class DeathRecordGenerator:
    """死亡记录生成器 - 生成优化的死亡记录文件"""
    
    def __init__(self):
        self.compressor = SnakeBodyCompressor()
        
    def generate_death_record(self, recorder: GameStateRecorder, death_info: Dict[str, Any]) -> Dict[str, Any]:
        """生成完整的死亡记录"""
        if recorder.last_food_state is None:
            raise ValueError("没有有效的回放起点状态")
            
        timestamp = datetime.datetime.now().strftime("%Y%m%d_%H%M%S")
        
        # 压缩蛇身数据
        snake_body = recorder.last_food_state['snake_body']
        compressed_body = self.compressor.compress_snake_body(snake_body)
        
        # 计算压缩比
        original_size = len(json.dumps(snake_body))
        compressed_size = len(json.dumps(compressed_body))
        compression_ratio = compressed_size / original_size if original_size > 0 else 1.0
        
        # 构建记录
        record = {
            "format_version": "3.0",
            "timestamp": timestamp,
            "metadata": {
                "game_duration": death_info['death_time'],
                "total_score": death_info['final_score'],
                "snake_max_length": death_info['final_snake_length'],
                "death_reason": death_info['death_reason']
            },
            "replay_segment": {
                "description": "从最后一次吃食物到死亡的完整回放段",
                "start_time": recorder.last_food_state['time'],
                "duration": death_info['death_time'] - recorder.last_food_state['time'],
                "start_state": {
                    "snake_body": snake_body[:10],  # 只保存前10节用于显示
                    "snake_body_compressed": compressed_body,
                    "snake_direction": recorder.last_food_state['snake_direction'],
                    "food_position": recorder.last_food_state['food_position'],
                    "score": recorder.last_food_state['score'],
                    "mode": recorder.last_food_state['mode'],
                    "snake_length": recorder.last_food_state['snake_length']
                },
                "events": recorder.events_since_last_food[1:],  # 排除food_eaten事件本身
                "end_state": {
                    "death_time": death_info['death_time'],
                    "death_position": death_info['death_position'],
                    "final_snake_length": death_info['final_snake_length'],
                    "collision_type": "self_collision" if "自己" in death_info['death_reason'] else "wall_collision"
                }
            },
            "optimization": {
                "compression_used": True,
                "compression_ratio": compression_ratio,
                "original_size_estimate": original_size,
                "compressed_size": compressed_size
            }
        }
        
        # 添加校验和
        record_str = json.dumps(record, sort_keys=True)
        checksum = hashlib.md5(record_str.encode()).hexdigest()[:12]
        
        record["validation"] = {
            "checksum": checksum,
            "replay_verified": False,  # 需要回放验证
            "verification_timestamp": None
        }
        
        return record
        
    def save_death_record(self, recorder: GameStateRecorder, death_info: Dict[str, Any]) -> str:
        """保存死亡记录到文件"""
        try:
            record = self.generate_death_record(recorder, death_info)
            timestamp = record["timestamp"]
            filename = f"death_record_v3_{timestamp}.json"
            
            with open(filename, 'w', encoding='utf-8') as f:
                json.dump(record, f, ensure_ascii=False, indent=2)
                
            print(f"✅ 死亡记录已保存到: {filename}")
            print(f"📊 压缩比: {record['optimization']['compression_ratio']:.2%}")
            print(f"📁 文件大小: {record['optimization']['compressed_size']} bytes")
            
            return filename
            
        except Exception as e:
            print(f"❌ 保存死亡记录失败: {e}")
            return None
